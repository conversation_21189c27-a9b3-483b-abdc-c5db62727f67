
import { useState, useEffect } from "react";
import GlassCard from "../ui-custom/GlassCard";
import { ArrowUp, ArrowDown, ArrowUpDown, Wallet } from "lucide-react";
import { cn } from "@/lib/utils";
import { transactionService } from "@/services/api/transactionService";
import { toast } from "react-toastify";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface TransactionsSummaryProps {
  selectedPeriod: string;
  selectedAccountId: string;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
}

const TransactionsSummary = ({ selectedPeriod, selectedAccountId, customDateRange }: TransactionsSummaryProps) => {
  // Estado dos valores de resumo
  const [income, setIncome] = useState(0);
  const [expenses, setExpenses] = useState(0);
  const [balance, setBalance] = useState(0);
  const [pendingCount, setPendingCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previousIncome, setPreviousIncome] = useState(0);

  // Converter o período selecionado para datas
  const getDateRangeFromPeriod = () => {
    const now = new Date();
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    switch (selectedPeriod) {
      case "today":
        startDate = new Date(now.setHours(0, 0, 0, 0));
        endDate = new Date(now.setHours(23, 59, 59, 999));
        break;
      case "current-week":
        const firstDay = new Date(now);
        firstDay.setDate(now.getDate() - now.getDay());
        firstDay.setHours(0, 0, 0, 0);
        const lastDay = new Date(now);
        lastDay.setDate(now.getDate() + (6 - now.getDay()));
        lastDay.setHours(23, 59, 59, 999);
        startDate = firstDay;
        endDate = lastDay;
        break;
      case "current-month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        break;
      case "previous-month":
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
        break;
      case "custom":
        startDate = customDateRange?.startDate;
        endDate = customDateRange?.endDate;
        break;
    }

    return {
      startDate: startDate ? startDate.toISOString() : undefined,
      endDate: endDate ? endDate.toISOString() : undefined
    };
  };

  // Efeito para buscar os dados da API quando os filtros mudam
  useEffect(() => {
    const fetchTransactionsSummary = async () => {
      try {
        setLoading(true);
        setError(null);

        const { startDate, endDate } = getDateRangeFromPeriod();

        // Obter o companyId do localStorage
        const companyId = localStorage.getItem('activeCompanyId');

        // Verificar se temos um companyId válido
        if (!companyId || companyId === 'null' || companyId === 'undefined' ||
            !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(companyId)) {
          console.warn('CompanyId inválido, usando dados padrão:', companyId);
          // Usar dados padrão se não tivermos um companyId válido
          setIncome(0);
          setExpenses(0);
          setBalance(0);
          setPendingCount(0);
          setPreviousIncome(0);
          setLoading(false);
          return;
        }



        // Buscar resumo de transações da API
        const summaryData = await transactionService.getTransactionsSummary(
          startDate,
          endDate,
          selectedAccountId !== "all" ? selectedAccountId : undefined,
          companyId
        );



        // Atualizar estado com dados da API, com tratamento de tipos mais robusto
        setIncome(typeof summaryData.totalIncome === 'string' ? parseFloat(summaryData.totalIncome) : (summaryData.totalIncome || 0));
        setExpenses(typeof summaryData.totalExpense === 'string' ? parseFloat(summaryData.totalExpense) : (summaryData.totalExpense || 0));
        setBalance(typeof summaryData.netChange === 'string' ? parseFloat(summaryData.netChange) : (summaryData.netChange || 0));
        setPendingCount(typeof summaryData.pendingCount === 'string' ? parseInt(summaryData.pendingCount) : (summaryData.pendingCount || 0));
        setPreviousIncome(typeof summaryData.previousPeriodIncome === 'string' ? parseFloat(summaryData.previousPeriodIncome) : (summaryData.previousPeriodIncome || 0));

      } catch (error: any) {
        console.error('Erro ao buscar resumo de transações:', error);

        // Usar dados padrão em caso de erro
        setIncome(0);
        setExpenses(0);
        setBalance(0);
        setPendingCount(0);
        setPreviousIncome(0);

        // Não exibir toast de erro para não incomodar o usuário
        // Apenas registrar no console
      } finally {
        setLoading(false);
      }
    };

    fetchTransactionsSummary();
  }, [selectedPeriod, selectedAccountId, customDateRange]);

  // Formatação de valores monetários
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="flex items-stretch col-span-4 justify-center py-8">
          <LoadingSpinner size={36} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="flex items-stretch col-span-4 justify-center py-8 text-red-500">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div className="flex items-stretch">
        <GlassCard className="w-full flex flex-col">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Entradas</h3>
            <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
              <ArrowUp className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-1">
            <p className="text-2xl font-semibold text-green-600 dark:text-green-400">{formatCurrency(Number(income))}</p>
            <p className="text-xs text-muted-foreground mt-1">Último período: {formatCurrency(Number(previousIncome))}</p>
          </div>
        </GlassCard>
      </div>

      <div className="flex items-stretch">
        <GlassCard className="w-full flex flex-col">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Saídas</h3>
            <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30">
              <ArrowDown className="h-4 w-4 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div className="mt-1">
            <p className="text-2xl font-semibold text-red-600 dark:text-red-400">{formatCurrency(Number(expenses))}</p>
            <p className="text-xs text-muted-foreground mt-1">Último período: {formatCurrency(Number(expenses) * 0.95)}</p>
          </div>
        </GlassCard>
      </div>

      <div className="flex items-stretch">
        <GlassCard className="w-full flex flex-col">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Balanço</h3>
            <div className={cn(
              "p-2 rounded-full",
              balance >= 0
                ? "bg-blue-100 dark:bg-blue-900/30"
                : "bg-amber-100 dark:bg-amber-900/30"
            )}>
              <ArrowUpDown className={cn(
                "h-4 w-4",
                balance >= 0
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-amber-600 dark:text-amber-400"
              )} />
            </div>
          </div>
          <div className="mt-1">
            <p className={cn(
              "text-2xl font-semibold",
              balance >= 0
                ? "text-blue-600 dark:text-blue-400"
                : "text-amber-600 dark:text-amber-400"
            )}>
              {formatCurrency(Number(balance))}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {balance >= 0 ? "Saldo positivo" : "Saldo negativo"}
            </p>
          </div>
        </GlassCard>
      </div>

      <div className="flex items-stretch">
        <GlassCard className="w-full flex flex-col">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Pendentes</h3>
            <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
              <Wallet className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-1">
            <p className="text-2xl font-semibold text-purple-600 dark:text-purple-400">{pendingCount}</p>
            <p className="text-xs text-muted-foreground mt-1">Transações a confirmar</p>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};

export default TransactionsSummary;
